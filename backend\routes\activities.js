import express from 'express';
import { Activity } from '../models/index.js';
import { authenticate, requireUser } from '../middleware/auth.js';
import { ValidationError } from '../middleware/errorHandler.js';

const router = express.Router();

/**
 * @route   GET /api/activities
 * @desc    Get activities for the authenticated user
 * @access  Private
 * @query   brandId - Filter by brand ID (optional)
 * @query   type - Filter by activity type (optional)
 * @query   limit - Number of activities to return (default: 20, max: 50)
 * @query   skip - Number of activities to skip for pagination (default: 0)
 */
router.get('/', authenticate, requireUser, async (req, res, next) => {
  try {
    const { brandId, type, limit = 20, skip = 0 } = req.query;
    
    // Validate and sanitize query parameters
    const parsedLimit = Math.min(parseInt(limit) || 20, 50); // Max 50 activities
    const parsedSkip = Math.max(parseInt(skip) || 0, 0);
    
    // Validate activity type if provided
    const validTypes = [
      'brand_created',
      'brand_updated', 
      'campaign_created',
      'campaign_content_generated',
      'quickpost_generated',
      'quickpost_updated',
      'content_regenerated',
      'documents_uploaded',
      'campaign_documents_uploaded',
      'quickpost_documents_uploaded',
      'brand_analyzed'
    ];
    
    if (type && !validTypes.includes(type)) {
      throw new ValidationError(`Invalid activity type. Must be one of: ${validTypes.join(', ')}`);
    }
    
    // Build query options
    const queryOptions = {
      brandId: brandId || undefined,
      type: type || undefined,
      limit: parsedLimit,
      skip: parsedSkip
    };
    
    // Remove undefined values
    Object.keys(queryOptions).forEach(key => {
      if (queryOptions[key] === undefined) {
        delete queryOptions[key];
      }
    });
    
    console.log('📊 Fetching activities for user:', req.user._id, 'with options:', queryOptions);
    
    // Fetch activities using the static method
    const activities = await Activity.findByUserId(req.user._id, queryOptions);
    
    // Transform activities to match frontend expectations
    const transformedActivities = activities.map(activity => ({
      id: activity._id,
      type: mapActivityTypeToFrontend(activity.type),
      title: activity.title,
      description: activity.description,
      time: activity.timeAgo,
      user: getUserDisplayName(activity.userId),
      brandName: activity.brandId?.name || null,
      metadata: activity.metadata,
      createdAt: activity.createdAt,
      originalType: activity.type // Keep original type for debugging
    }));
    
    // Get total count for pagination (optional)
    const totalQuery = { userId: req.user._id };
    if (brandId) totalQuery.brandId = brandId;
    if (type) totalQuery.type = type;
    
    const totalCount = await Activity.countDocuments(totalQuery);
    
    res.status(200).json({
      success: true,
      message: 'Activities retrieved successfully',
      data: {
        activities: transformedActivities,
        pagination: {
          total: totalCount,
          limit: parsedLimit,
          skip: parsedSkip,
          hasMore: (parsedSkip + parsedLimit) < totalCount
        }
      }
    });
    
  } catch (error) {
    console.error('❌ Error fetching activities:', error);
    next(error);
  }
});

/**
 * @route   GET /api/activities/stats
 * @desc    Get activity statistics for the authenticated user
 * @access  Private
 */
router.get('/stats', authenticate, requireUser, async (req, res, next) => {
  try {
    const { brandId } = req.query;
    
    const baseQuery = { userId: req.user._id };
    if (brandId) {
      baseQuery.brandId = brandId;
    }
    
    // Get activity counts by type for the last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const stats = await Activity.aggregate([
      {
        $match: {
          ...baseQuery,
          createdAt: { $gte: thirtyDaysAgo }
        }
      },
      {
        $group: {
          _id: '$type',
          count: { $sum: 1 }
        }
      },
      {
        $sort: { count: -1 }
      }
    ]);
    
    const totalActivities = await Activity.countDocuments(baseQuery);
    const recentActivities = await Activity.countDocuments({
      ...baseQuery,
      createdAt: { $gte: thirtyDaysAgo }
    });
    
    res.status(200).json({
      success: true,
      message: 'Activity statistics retrieved successfully',
      data: {
        totalActivities,
        recentActivities,
        activityBreakdown: stats,
        period: '30 days'
      }
    });
    
  } catch (error) {
    console.error('❌ Error fetching activity stats:', error);
    next(error);
  }
});

/**
 * Map backend activity types to frontend display types
 */
function mapActivityTypeToFrontend(backendType) {
  const typeMap = {
    'brand_created': 'creation',
    'brand_updated': 'update',
    'brand_analyzed': 'analysis',
    'campaign_created': 'creation',
    'campaign_content_generated': 'generation',
    'quickpost_generated': 'generation',
    'quickpost_updated': 'update',
    'content_regenerated': 'regeneration',
    'documents_uploaded': 'upload',
    'campaign_documents_uploaded': 'upload',
    'quickpost_documents_uploaded': 'upload'
  };
  
  return typeMap[backendType] || 'activity';
}

/**
 * Get user display name from user object
 */
function getUserDisplayName(user) {
  if (!user) return 'Unknown User';
  
  if (user.firstName && user.lastName) {
    return `${user.firstName} ${user.lastName}`;
  }
  
  if (user.firstName) {
    return user.firstName;
  }
  
  if (user.email) {
    return user.email.split('@')[0]; // Use email username as fallback
  }
  
  return 'Unknown User';
}

export default router;
