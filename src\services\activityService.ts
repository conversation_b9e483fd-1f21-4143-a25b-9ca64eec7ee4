import api from './api';

// Activity interfaces
export interface Activity {
  id: string;
  type: string;
  title: string;
  description: string;
  time: string;
  user: string;
  brandName?: string;
  metadata?: any;
  createdAt: string;
  originalType: string;
}

export interface ActivityStats {
  totalActivities: number;
  recentActivities: number;
  activityBreakdown: Array<{
    _id: string;
    count: number;
  }>;
  period: string;
}

export interface ActivityResponse {
  success: boolean;
  message: string;
  data: {
    activities: Activity[];
    pagination: {
      total: number;
      limit: number;
      skip: number;
      hasMore: boolean;
    };
  };
}

export interface ActivityStatsResponse {
  success: boolean;
  message: string;
  data: ActivityStats;
}

// Activity service functions
export const activityService = {
  /**
   * Get activities for the current user
   */
  getActivities: async (options: {
    brandId?: string;
    type?: string;
    limit?: number;
    skip?: number;
  } = {}): Promise<ActivityResponse> => {
    try {
      const params = new URLSearchParams();
      
      if (options.brandId) params.append('brandId', options.brandId);
      if (options.type) params.append('type', options.type);
      if (options.limit) params.append('limit', options.limit.toString());
      if (options.skip) params.append('skip', options.skip.toString());

      const response = await api.get(`/activities?${params.toString()}`);
      return response.data;
    } catch (error: any) {
      console.error('❌ Error fetching activities:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch activities');
    }
  },

  /**
   * Get activity statistics
   */
  getActivityStats: async (brandId?: string): Promise<ActivityStatsResponse> => {
    try {
      const params = brandId ? `?brandId=${brandId}` : '';
      const response = await api.get(`/activities/stats${params}`);
      return response.data;
    } catch (error: any) {
      console.error('❌ Error fetching activity stats:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch activity statistics');
    }
  },

  /**
   * Get recent activities (last 20)
   */
  getRecentActivities: async (brandId?: string): Promise<Activity[]> => {
    try {
      const response = await activityService.getActivities({
        brandId,
        limit: 20,
        skip: 0
      });
      return response.data.activities;
    } catch (error) {
      console.error('❌ Error fetching recent activities:', error);
      return [];
    }
  }
};

// Activity type mapping for icons and colors
export const getActivityConfig = (type: string) => {
  const configs = {
    creation: {
      icon: 'Plus',
      color: 'text-success-500',
      bgColor: 'bg-success-50'
    },
    update: {
      icon: 'Edit',
      color: 'text-info-500',
      bgColor: 'bg-info-50'
    },
    generation: {
      icon: 'Zap',
      color: 'text-brand-500',
      bgColor: 'bg-brand-50'
    },
    regeneration: {
      icon: 'RefreshCw',
      color: 'text-warning-500',
      bgColor: 'bg-warning-50'
    },
    upload: {
      icon: 'Upload',
      color: 'text-purple-500',
      bgColor: 'bg-purple-50'
    },
    analysis: {
      icon: 'Brain',
      color: 'text-indigo-500',
      bgColor: 'bg-indigo-50'
    },
    activity: {
      icon: 'Activity',
      color: 'text-gray-500',
      bgColor: 'bg-gray-50'
    }
  };

  return configs[type as keyof typeof configs] || configs.activity;
};

// Format relative time
export const formatRelativeTime = (dateString: string): string => {
  const now = new Date();
  const date = new Date(dateString);
  const diffMs = now.getTime() - date.getTime();
  const diffMins = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (diffMins < 1) return 'Just now';
  if (diffMins < 60) return `${diffMins} minute${diffMins > 1 ? 's' : ''} ago`;
  if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
  if (diffDays < 7) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;

  return date.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
  });
};

export default activityService;
