import express from 'express';
import axios from 'axios';
import { authenticate, requireUser } from '../middleware/auth.js';
import { ValidationError } from '../middleware/errorHandler.js';
import { logBrandAnalyzed, logQuickPostGenerated, logContentRegenerated } from '../utils/activityLogger.js';
import QuickPost from '../models/QuickPost.js';
import Campaign from '../models/Campaign.js';
import Brand from '../models/Brand.js';

const router = express.Router();

// Document processing is now handled directly in upload.js
// This route is removed to avoid code duplication

/**
 * @route   POST /api/ai/analyze-company
 * @desc    Analyze company using AI service and return brand data
 * @access  Private
 */
router.post('/analyze-company', authenticate, requireUser, async (req, res, next) => {
  try {
    const { companyName, websiteUrl } = req.body;

    // Validation
    if (!companyName || !websiteUrl) {
      throw new ValidationError('Company name and website URL are required');
    }

    // Basic URL validation - just check if it's not empty
    if (!websiteUrl.trim()) {
      throw new ValidationError('Website URL cannot be empty');
    }

    console.log(`🤖 Analyzing company: ${companyName} - ${websiteUrl}`);

    try {
      // Call AI analysis service
      const aiResponse = await axios.post(`${process.env.AI_SERVICE_URL}/api/analyze-company`, {
        companyName,
        websiteUrl
      }, {
        headers: {
          'Content-Type': 'application/json'
        }
      });

      console.log('✅ AI analysis completed successfully');

      // Log brand analysis activity (without brandId since this is pre-brand creation)
      await logBrandAnalyzed(req.user._id, null, companyName);

      // Return the AI response data
      res.status(200).json({
        success: true,
        message: 'Company analysis completed successfully',
        data: aiResponse.data.data || aiResponse.data,
        aiGenerated: true
      });

    } catch (aiError) {
      console.error('❌ AI service error:', aiError.message);

      // No more fallback to mock data - throw error instead

      // Handle all AI service errors consistently
      if (aiError.response) {
        // AI service returned an error response
        const errorMessage = aiError.response.data?.message || 'AI analysis failed';
        throw new Error(`AI analysis error: ${errorMessage}`);
      } else if (aiError.code === 'ECONNREFUSED' || aiError.code === 'ENOTFOUND') {
        throw new Error('AI analysis service is currently unavailable. Please try again later or use manual entry.');
      } else {
        throw new Error('AI analysis service encountered an error. Please try again later or use manual entry.');
      }
    }

  } catch (error) {
    // If it's our custom error, pass it through
    if (error instanceof ValidationError) {
      next(error);
    } else {
      // For AI service errors, return a user-friendly response
      res.status(503).json({
        success: false,
        error: {
          message: error.message,
          code: 'AI_SERVICE_ERROR',
          fallbackToManual: true
        }
      });
    }
  }
});

/**
 * @route   POST /api/ai/research-insights
 * @desc    Generate research insights for campaign creation
 * @access  Private
 */
router.post('/research-insights', authenticate, requireUser, async (req, res, next) => {
  console.log('Backend: Received research-insights request');
  console.log('Request body keys:', Object.keys(req.body));
  console.log('User ID:', req.user?._id);

  try {
    console.log('🔍 Generating research insights...');

    // Log basic request info
    console.log('Payload validation: ✓ Request structure validated');

    // Call AI research insights service
    console.log('Calling AI service at:', `${process.env.AI_SERVICE_URL}/api/research-insights`);
    console.log('Request payload:', JSON.stringify(req.body, null, 2));

    const aiResponse = await axios.post(`${process.env.AI_SERVICE_URL}/api/research-insights`, req.body, {
      headers: {
        'Content-Type': 'application/json'
      }
      // No timeout - let it wait as long as needed
    });

    console.log('✅ Research insights generated successfully');
    console.log('📊 Response size:', JSON.stringify(aiResponse.data).length, 'characters');

    // Send response to frontend
    const responseData = {
      success: true,
      message: 'Research insights generated successfully',
      data: aiResponse.data.data || aiResponse.data
    };

    res.status(200).json(responseData);

  } catch (error) {
    console.error('❌ Research insights error:', error.message);
    console.error('Error response status:', error.response?.status);
    console.error('Error response data:', JSON.stringify(error.response?.data, null, 2));

    // Return proper error response - no mock data fallback
    let errorMessage = 'Failed to generate research insights';
    let errorCode = 'AI_SERVICE_ERROR';

    if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
      errorMessage = 'AI service is currently unavailable. Please try again later.';
      errorCode = 'SERVICE_UNAVAILABLE';
    } else if (error.code === 'ECONNABORTED' || error.code === 'ETIMEDOUT') {
      errorMessage = 'Request timed out. The AI service is taking longer than expected.';
      errorCode = 'REQUEST_TIMEOUT';
    } else if (error.code === 'ECONNRESET' || error.message.includes('socket hang up')) {
      errorMessage = 'Connection lost to AI service. Please try again.';
      errorCode = 'CONNECTION_LOST';
    } else if (error.response?.status === 429) {
      errorMessage = 'Too many requests. Please wait a moment and try again.';
      errorCode = 'RATE_LIMITED';
    } else if (error.response?.status >= 500) {
      errorMessage = 'AI service is experiencing issues. Please try again.';
      errorCode = 'SERVER_ERROR';
    }

    res.status(503).json({
      success: false,
      error: {
        message: errorMessage,
        code: errorCode,
        canRetry: true
      }
    });
  }
});

/**
 * @route   POST /api/ai/campaign-strategy
 * @desc    Generate campaign strategy based on research insights
 * @access  Private
 */
router.post('/campaign-strategy', authenticate, requireUser, async (req, res, next) => {
  try {
    console.log('🎯 Generating campaign strategy...');

    // Call AI campaign strategy service
    const aiResponse = await axios.post(`${process.env.AI_SERVICE_URL}/api/campaign-strategy`, req.body, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 240000, // 4 minutes timeout for AI processing
      maxContentLength: Infinity,
      maxBodyLength: Infinity
    });

    console.log('✅ Campaign strategy generated successfully');

    res.status(200).json({
      success: true,
      message: 'Campaign strategy generated successfully',
      data: aiResponse.data.data || aiResponse.data
    });

  } catch (error) {
    console.error('❌ Campaign strategy error:', error.message);

    // Return mock data for development if AI service is unavailable
    if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND' || error.code === 'ECONNABORTED') {
      console.log('🔧 AI service unavailable, returning mock campaign strategy...');

      const mockStrategy = {
        "campaignThemes": [
          {
            "theme": "Innovation Leadership",
            "description": "Position the brand as a forward-thinking leader in technological innovation",
            "tags": ["innovation", "technology", "leadership", "future"]
          },
          {
            "theme": "Customer Success Stories",
            "description": "Highlight real customer achievements and transformations",
            "tags": ["success", "testimonials", "results", "transformation"]
          }
        ],
        "channelSpecificStrategies": [
          {
            "channel": "linkedin",
            "approach": "Professional thought leadership and industry insights",
            "contentTypes": ["articles", "infographics", "video testimonials"],
            "contentFrequency": "3-4 posts per week",
            "targetAudience": "Business decision makers and industry professionals"
          },
          {
            "channel": "twitter",
            "approach": "Real-time engagement and industry conversations",
            "contentTypes": ["threads", "quick tips", "industry news commentary"],
            "contentFrequency": "Daily posts with 2-3 engagement threads per week",
            "targetAudience": "Tech-savvy professionals and early adopters"
          }
        ]
      };

      return res.status(200).json({
        success: true,
        message: 'Campaign strategy generated successfully (mock data)',
        data: mockStrategy,
        isMockData: true
      });
    }

    res.status(503).json({
      success: false,
      error: {
        message: 'Failed to generate campaign strategy',
        code: 'AI_SERVICE_ERROR'
      }
    });
  }
});

/**
 * @route   POST /api/ai/campaign-calendar-draft
 * @desc    Generate content calendar draft based on campaign strategy
 * @access  Private
 */
router.post('/campaign-calendar-draft', authenticate, requireUser, async (req, res, next) => {
  try {
    console.log('📅 Generating content calendar draft...');

    // Call AI content calendar service
    const aiResponse = await axios.post(`${process.env.AI_SERVICE_URL}/api/content-calendar-draft`, req.body, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 180000, // 3 minutes timeout for AI processing
      maxContentLength: Infinity,
      maxBodyLength: Infinity
    });

    console.log('✅ Content calendar draft generated successfully');

    res.status(200).json({
      success: true,
      message: 'Content calendar draft generated successfully',
      data: aiResponse.data.data || aiResponse.data
    });

  } catch (error) {
    console.error('❌ Content calendar error:', error.message);
    console.error('Error response status:', error.response?.status);
    console.error('Error response data:', JSON.stringify(error.response?.data, null, 2));

    // Return mock data for development if AI service is unavailable
    if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND' || error.code === 'ECONNABORTED' || error.code === 'ECONNRESET' || error.message.includes('socket hang up')) {
      console.log('🔧 AI service unavailable, returning mock content calendar...');

      const mockCalendar = {
        "contentCalendar": [
          {
            "date": "2025-07-20T09:00:00.000Z",
            "channel": "linkedin",
            "contentType": "article",
            "topic": "The Future of AI in Marketing: Trends to Watch",
            "hashtags": ["#AIMarketing", "#Innovation", "#MarketingTrends", "#Technology"]
          },
          {
            "date": "2025-07-21T14:00:00.000Z",
            "channel": "twitter",
            "contentType": "thread",
            "topic": "5 Ways AI is Transforming Customer Experience",
            "hashtags": ["#CustomerExperience", "#AI", "#CX", "#Innovation"]
          },
          {
            "date": "2025-07-22T10:00:00.000Z",
            "channel": "linkedin",
            "contentType": "infographic",
            "topic": "ROI of Marketing Automation: By the Numbers",
            "hashtags": ["#MarketingROI", "#Automation", "#DataDriven", "#Results"]
          },
          {
            "date": "2025-07-23T16:00:00.000Z",
            "channel": "twitter",
            "contentType": "quick tip",
            "topic": "Quick Tip: Optimizing Your Marketing Funnel",
            "hashtags": ["#MarketingTips", "#Optimization", "#Growth", "#Strategy"]
          }
        ]
      };

      return res.status(200).json({
        success: true,
        message: 'Content calendar draft generated successfully (mock data)',
        data: mockCalendar,
        isMockData: true
      });
    }

    res.status(503).json({
      success: false,
      error: {
        message: 'Failed to generate content calendar draft',
        code: 'AI_SERVICE_ERROR'
      }
    });
  }
});

/**
 * @route   GET /api/ai/status
 * @desc    Check AI service status
 * @access  Private
 */
router.get('/status', authenticate, requireUser, async (req, res, next) => {
  try {
    const aiServiceUrl = process.env.AI_SERVICE_URL;

    if (!aiServiceUrl) {
      return res.status(200).json({
        success: true,
        data: {
          available: false,
          message: 'AI service URL not configured'
        }
      });
    }

    try {
      // Try to ping the AI service
      const response = await axios.get(`${aiServiceUrl}/health`);

      res.status(200).json({
        success: true,
        data: {
          available: true,
          message: 'AI service is available',
          serviceUrl: aiServiceUrl,
          status: response.data
        }
      });

    } catch (error) {
      res.status(200).json({
        success: true,
        data: {
          available: false,
          message: 'AI service is currently unavailable',
          error: error.message
        }
      });
    }

  } catch (error) {
    next(error);
  }
});

/**
 * @route   POST /api/ai/quick-generate
 * @desc    Generate quick post content using AI service
 * @access  Private
 */
router.post('/quick-generate', authenticate, requireUser, async (req, res, next) => {
  try {
    console.log('⚡ Generating quick post content...');

    const {
      brandId,
      brandInformation,
      brandIdentity,
      'products/services': products,
      ICPs,
      quickContentInfo,
      quickContentFiles,
      selectedBrandFiles
    } = req.body;

    // Validate required fields
    if (!brandId || !brandInformation || !brandIdentity || !quickContentInfo) {
      throw new ValidationError('brandId, brandInformation, brandIdentity, and quickContentInfo are required');
    }

    // Validate quickContentInfo required fields
    if (!quickContentInfo.postDate || !quickContentInfo.contentInstructions || !quickContentInfo.Channel) {
      throw new ValidationError('postDate, contentInstructions, and Channel are required in quickContentInfo');
    }

    // Log basic request info
    console.log('Payload validation: ✓ Request structure validated');
    console.log('Quick post info:', {
      postDate: quickContentInfo.postDate,
      channel: quickContentInfo.Channel,
      hasProducts: products && products.length > 0,
      hasICPs: ICPs && ICPs.length > 0,
      hasQuickFiles: quickContentFiles && quickContentFiles.length > 0,
      hasBrandFiles: selectedBrandFiles && selectedBrandFiles.length > 0
    });

    // Call AI quick post generation service
    console.log('Calling AI service at:', `${process.env.AI_SERVICE_URL}/api/quick-generate`);
    console.log('Request payload:', JSON.stringify(req.body, null, 2));

    const aiResponse = await axios.post(`${process.env.AI_SERVICE_URL}/api/quick-generate`, req.body, {
      headers: {
        'Content-Type': 'application/json'
      }
      // No timeout - let it wait as long as needed
    });

    console.log('✅ Quick post content generated successfully');
    console.log('📊 Response size:', JSON.stringify(aiResponse.data).length, 'characters');

    // Save quick post to database
    try {
      const quickPostData = {
        userId: req.user._id,
        brandId,
        postDate: new Date(quickContentInfo.postDate),
        contentInstructions: quickContentInfo.contentInstructions,
        destinationUrl: quickContentInfo.destinationUrl || '',
        channel: quickContentInfo.Channel,
        selectedProducts: products ? products.map(p => p.name || p.id || p._id) : [],
        selectedICPs: ICPs ? ICPs.map(icp => icp.profileName || icp.id || icp._id) : [],
        selectedBrandFiles: selectedBrandFiles || [],
        quickContentFiles: quickContentFiles || [],
        generatedContent: aiResponse.data.data || aiResponse.data,
        status: 'generated',
        isAIGenerated: true,
        progress: 100
      };

      const savedQuickPost = await QuickPost.create(quickPostData);
      console.log('💾 Quick post saved to database:', savedQuickPost._id);

      // Log quick post generation activity
      const generatedContent = aiResponse.data.data || aiResponse.data;
      const contentType = generatedContent.contentType || 'Post';
      await logQuickPostGenerated(req.user._id, brandId, savedQuickPost._id, quickContentInfo.Channel, contentType);

      // Send response to frontend with saved post ID
      const responseData = {
        success: true,
        message: 'Quick post content generated and saved successfully',
        data: aiResponse.data.data || aiResponse.data,
        quickPostId: savedQuickPost._id
      };

      res.status(200).json(responseData);
    } catch (dbError) {
      console.error('❌ Failed to save quick post to database:', dbError);

      // Still return success for AI generation but indicate DB save failed
      const responseData = {
        success: true,
        message: 'Quick post content generated successfully but failed to save to database',
        data: aiResponse.data.data || aiResponse.data,
        dbError: dbError.message
      };

      res.status(200).json(responseData);
    }

  } catch (error) {
    console.error('❌ Quick post generation error:', error.message);
    console.error('Error response status:', error.response?.status);
    console.error('Error response data:', JSON.stringify(error.response?.data, null, 2));

    // Return proper error response
    let errorMessage = 'Failed to generate quick post content';
    let errorCode = 'AI_SERVICE_ERROR';

    if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
      errorMessage = 'AI service is currently unavailable. Please try again later.';
      errorCode = 'SERVICE_UNAVAILABLE';
    } else if (error.code === 'ECONNABORTED' || error.code === 'ETIMEDOUT') {
      errorMessage = 'Request timed out. The AI service is taking longer than expected.';
      errorCode = 'REQUEST_TIMEOUT';
    } else if (error.code === 'ECONNRESET' || error.message.includes('socket hang up')) {
      errorMessage = 'Connection lost to AI service. Please try again.';
      errorCode = 'CONNECTION_LOST';
    } else if (error.response?.status === 429) {
      errorMessage = 'Too many requests. Please wait a moment and try again.';
      errorCode = 'RATE_LIMITED';
    } else if (error.response?.status >= 500) {
      errorMessage = 'AI service is experiencing issues. Please try again.';
      errorCode = 'SERVER_ERROR';
    }

    res.status(503).json({
      success: false,
      error: {
        message: errorMessage,
        code: errorCode,
        details: error.response?.data || error.message
      }
    });
  }
});

/**
 * @route   POST /api/ai/regenerate-content
 * @desc    Regenerate content using AI service with user instruction (simplified schema)
 * @access  Private
 */
router.post('/regenerate-content', authenticate, requireUser, async (req, res, next) => {
  try {
    console.log('🔄 Starting content regeneration...');

    const {
      brandId,
      contentId, // Format: "campaignId-contentIndex" or "quickPostId-contentIndex"
      contentType, // "campaign" or "quickpost"
      old_gen_content,
      additional_instruction
    } = req.body;

    // Validate required fields
    if (!brandId || !contentId || !contentType || !old_gen_content || !additional_instruction) {
      throw new ValidationError('brandId, contentId, contentType, old_gen_content, and additional_instruction are required');
    }

    // Fetch brand data
    const brand = await Brand.findOne({ _id: brandId, userId: req.user._id });
    if (!brand) {
      throw new ValidationError('Brand not found or access denied');
    }

    let sourceData, contentIndex, contentItem;

    if (contentType === 'campaign') {
      // Parse campaign content ID
      const [campaignId, indexStr] = contentId.split('-');
      contentIndex = parseInt(indexStr);

      const campaign = await Campaign.findOne({ _id: campaignId, userId: req.user._id });
      if (!campaign) {
        throw new ValidationError('Campaign not found or access denied');
      }

      if (!campaign.generatedContent[contentIndex]) {
        throw new ValidationError('Content not found at specified index');
      }

      contentItem = campaign.generatedContent[contentIndex];
      sourceData = {
        type: 'campaign',
        campaign,
        brand,
        researchInsights: campaign.researchInsights,
        campaignStrategy: campaign.campaignStrategy,
        campaignFiles: campaign.campaignFiles || [],
        selectedBrandFiles: campaign.selectedBrandFiles || []
      };

    } else if (contentType === 'quickpost') {
      // Parse quick post content ID
      const [quickPostId, indexStr] = contentId.split('-');
      contentIndex = parseInt(indexStr);

      const quickPost = await QuickPost.findOne({ _id: quickPostId, userId: req.user._id });
      if (!quickPost) {
        throw new ValidationError('Quick post not found or access denied');
      }

      if (!quickPost.generatedContent[contentIndex]) {
        throw new ValidationError('Content not found at specified index');
      }

      contentItem = quickPost.generatedContent[contentIndex];
      sourceData = {
        type: 'quickpost',
        quickPost,
        brand,
        quickContentFiles: quickPost.quickContentFiles || [],
        selectedBrandFiles: quickPost.selectedBrandFiles || []
      };

    } else {
      throw new ValidationError('Invalid content type. Must be "campaign" or "quickpost"');
    }

    // Build regeneration request according to new simplified schema
    const regenerationRequest = {
      brandId,
      brandInformation: {
        name: brand.name,
        about: brand.about,
        story: brand.story,
        location: brand.location,
        targetGeography: brand.targetGeography,
        website: brand.website,
        size: brand.size,
        type: brand.type,
        industry: brand.industry,
        socialMedia: brand.socialMedia || [],
        contactInfo: brand.contactInfo || { phone: [], email: [] },
        keyTeamMembers: brand.keyTeamMembers || []
      },
      brandIdentity: brand.brandIdentity || {
        brandVoice: [],
        coreMessagingPillars: '',
        primaryKeywords: '',
        negativeKeywords: ''
      },
      old_gen_content,
      additional_instruction,
      selectedBrandFiles: sourceData.selectedBrandFiles || []
    };

    // Add products/services only if brand has products data (now optional)
    if (brand.products && brand.products.length > 0) {
      regenerationRequest['products/services'] = brand.products;
    }

    // Add content-type specific file references
    if (contentType === 'campaign') {
      regenerationRequest.campaignFiles = sourceData.campaignFiles || [];
    } else if (contentType === 'quickpost') {
      regenerationRequest.quickContentFiles = sourceData.quickContentFiles || [];
    }

    console.log('📤 Calling AI regeneration service...');
    console.log('🔍 AI Service URL:', process.env.AI_SERVICE_URL);
    console.log('📋 Request payload:', JSON.stringify(regenerationRequest, null, 2));

    // Check if AI service URL is configured
    if (!process.env.AI_SERVICE_URL) {
      console.warn('⚠️ AI_SERVICE_URL not configured');
      throw new Error('AI service not configured');
    }

    try {
      // Call AI regeneration service
      const aiResponse = await axios.post(`${process.env.AI_SERVICE_URL}/api/content/regenerate`, regenerationRequest, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 300000 // 5 minutes timeout
      });

      console.log('✅ Content regeneration completed successfully');

      const regeneratedContent = aiResponse.data.data || [];
      if (!regeneratedContent.length) {
        throw new Error('No content returned from AI service');
      }

      // Get the regenerated content (should be single item)
      const newContent = regeneratedContent[0];

      // Create new version using model methods
      if (contentType === 'campaign') {
        await sourceData.campaign.createContentVersion(
          contentIndex,
          newContent,
          additional_instruction,
          'ai_regeneration'
        );

        // Refresh campaign data
        await sourceData.campaign.populate('brandId', 'name industry');

        // Log content regeneration activity
        await logContentRegenerated(req.user._id, brandId, contentId, 'campaign', additional_instruction);

        res.status(200).json({
          success: true,
          message: 'Content regenerated successfully',
          data: {
            contentId,
            newVersion: contentItem.version,
            regeneratedContent: newContent,
            campaign: sourceData.campaign
          }
        });

      } else if (contentType === 'quickpost') {
        await sourceData.quickPost.createContentVersion(
          contentIndex,
          newContent,
          additional_instruction,
          'ai_regeneration'
        );

        // Log content regeneration activity
        await logContentRegenerated(req.user._id, brandId, contentId, 'quickpost', additional_instruction);

        res.status(200).json({
          success: true,
          message: 'Content regenerated successfully',
          data: {
            contentId,
            newVersion: contentItem.version,
            regeneratedContent: newContent,
            quickPost: sourceData.quickPost
          }
        });
      }

    } catch (aiError) {
      console.error('❌ AI regeneration error:', aiError.message);
      console.error('🔍 Error details:', {
        status: aiError.response?.status,
        statusText: aiError.response?.statusText,
        data: aiError.response?.data,
        code: aiError.code
      });

      // Log detailed error information for debugging
      if (aiError.response?.data?.detail) {
        console.error('🔍 Detailed validation errors:', JSON.stringify(aiError.response.data.detail, null, 2));
      }

      // Handle specific AI service errors
      let errorMessage = 'Content regeneration failed. Please try again.';
      let errorCode = 'AI_SERVICE_ERROR';

      if (aiError.code === 'ECONNREFUSED' || aiError.code === 'ENOTFOUND') {
        errorMessage = 'AI service is currently unavailable. Please try again later.';
        errorCode = 'SERVICE_UNAVAILABLE';
      } else if (aiError.code === 'ECONNABORTED') {
        errorMessage = 'Request timed out. Please try again.';
        errorCode = 'TIMEOUT';
      } else if (aiError.response?.status === 400) {
        errorMessage = 'Invalid request data. Please check your input.';
        errorCode = 'INVALID_REQUEST';
      } else if (aiError.response?.status === 429) {
        errorMessage = 'Too many requests. Please wait a moment and try again.';
        errorCode = 'RATE_LIMITED';
      }

      res.status(503).json({
        success: false,
        error: {
          message: errorMessage,
          code: errorCode,
          details: aiError.response?.data || aiError.message
        }
      });
    }

  } catch (error) {
    console.error('❌ Content regeneration error:', error);
    next(error);
  }
});

export default router;