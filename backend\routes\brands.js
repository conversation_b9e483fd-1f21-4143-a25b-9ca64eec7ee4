import express from 'express';
import { Brand, Organization } from '../models/index.js';
import { authenticate, requireUser, requireBrandOwnership } from '../middleware/auth.js';
import { ValidationError, NotFoundError } from '../middleware/errorHandler.js';
import { logBrandCreated, logBrandUpdated, logBrandAnalyzed } from '../utils/activityLogger.js';

const router = express.Router();

/**
 * @route   GET /api/brands/user/:userId
 * @desc    Get brands for a specific user
 * @access  Private
 */
router.get('/user/:userId', authenticate, requireUser, async (req, res, next) => {
  try {
    const { userId } = req.params;

    // Users can only access their own brand (unless admin)
    if (req.user._id.toString() !== userId && req.user.role !== 'admin') {
      throw new ValidationError('Access denied. You can only access your own brand data');
    }

    const brands = await Brand.findByUserId(userId);

    console.log('🏢 API: Found brands for user:', brands?.length || 0);
    console.log('🏢 API: Brand details:', brands?.map(b => ({
      id: b._id,
      name: b.name,
      userId: b.userId
    })));

    if (!brands || brands.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'No brands found for this user',
        requiresBrandCreation: true
      });
    }

    res.status(200).json({
      success: true,
      message: 'Brands retrieved successfully',
      data: { brands }
    });

  } catch (error) {
    next(error);
  }
});

/**
 * @route   GET /api/brands/me
 * @desc    Get current user's brand
 * @access  Private
 */
router.get('/me', authenticate, requireUser, async (req, res, next) => {
  try {
    const brands = await Brand.findByUserId(req.user._id);

    if (!brands || brands.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Brand not found. Please create your brand profile first.',
        requiresBrandCreation: true
      });
    }

    // Return the first brand for backward compatibility with frontend
    // In the future, you can modify this to return all brands or let user select
    const brand = brands[0];

    res.status(200).json({
      success: true,
      message: 'Brand retrieved successfully',
      data: { brand }
    });

  } catch (error) {
    next(error);
  }
});

/**
 * @route   POST /api/brands
 * @desc    Create a new brand
 * @access  Private
 */
router.post('/', authenticate, requireUser, async (req, res, next) => {
  try {
    const userId = req.user._id;
    let organizationId = req.user.organizationId; // Get organization ID from authenticated user

    // If user doesn't have an organizationId, assign them to the default organization
    if (!organizationId) {
      const defaultOrg = await Organization.findOne({ slug: 'default-org' });
      if (defaultOrg) {
        organizationId = defaultOrg._id;
        // Update user with default organization
        req.user.organizationId = organizationId;
        await req.user.save();
        console.log(`📝 Assigned user ${req.user.email} to default organization`);
      }
    }

    // Validate required fields
    const { name, industry, size, type } = req.body;
    if (!name || !industry || !size || !type) {
      throw new ValidationError('Name, industry, size, and type are required fields');
    }

    // Create brand data
    const brandData = {
      userId,
      organizationId, // Set organization ID from user (or default)
      ...req.body
    };

    // Create new brand
    const brand = new Brand(brandData);
    await brand.save();

    // Update user's brandId to the latest created brand
    req.user.brandId = brand._id;
    await req.user.save();

    // Log brand creation activity
    await logBrandCreated(userId, brand._id, brand.name);

    res.status(201).json({
      success: true,
      message: 'Brand created successfully',
      data: { brand }
    });

  } catch (error) {
    next(error);
  }
});

/**
 * @route   PUT /api/brands/:brandId
 * @desc    Update an existing brand
 * @access  Private
 */
router.put('/:brandId', authenticate, requireUser, requireBrandOwnership, async (req, res, next) => {
  try {
    const { brandId } = req.params;

    // Get updated field names for activity logging
    const updatedFields = Object.keys(req.body).filter(key => key !== 'updatedAt');

    // Update brand
    const updatedBrand = await Brand.findByIdAndUpdate(
      brandId,
      { ...req.body, updatedAt: new Date() },
      { new: true, runValidators: true }
    );

    if (!updatedBrand) {
      throw new NotFoundError('Brand not found');
    }

    // Log brand update activity
    await logBrandUpdated(req.user._id, brandId, updatedBrand.name, updatedFields);

    res.status(200).json({
      success: true,
      message: 'Brand updated successfully',
      data: { brand: updatedBrand }
    });

  } catch (error) {
    next(error);
  }
});

/**
 * @route   DELETE /api/brands/:brandId
 * @desc    Delete a brand
 * @access  Private
 */
router.delete('/:brandId', authenticate, requireUser, requireBrandOwnership, async (req, res, next) => {
  try {
    const { brandId } = req.params;

    const brand = await Brand.findByIdAndDelete(brandId);

    if (!brand) {
      throw new NotFoundError('Brand not found');
    }

    // Remove brand reference from user
    req.user.brandId = null;
    await req.user.save();

    res.status(200).json({
      success: true,
      message: 'Brand deleted successfully'
    });

  } catch (error) {
    next(error);
  }
});

/**
 * @route   GET /api/brands/:brandId/status
 * @desc    Check if brand profile is complete
 * @access  Private
 */
router.get('/:brandId/status', authenticate, requireUser, requireBrandOwnership, async (req, res, next) => {
  try {
    const brand = req.brand; // Set by requireBrandOwnership middleware

    const status = {
      isComplete: brand.isComplete(),
      completionPercentage: calculateCompletionPercentage(brand),
      missingFields: getMissingFields(brand),
      stats: {
        productCount: brand.productCount,
        competitorCount: brand.competitorCount,
        icpCount: brand.icpCount
      }
    };

    res.status(200).json({
      success: true,
      message: 'Brand status retrieved successfully',
      data: { status }
    });

  } catch (error) {
    next(error);
  }
});

// Helper functions
function calculateCompletionPercentage(brand) {
  const requiredFields = ['name', 'industry', 'size', 'type'];
  const optionalFields = ['about', 'story', 'location', 'website'];
  const arrayFields = ['products', 'icps', 'competitors'];

  let completed = 0;
  let total = requiredFields.length + optionalFields.length + arrayFields.length;

  // Check required fields
  requiredFields.forEach(field => {
    if (brand[field]) completed++;
  });

  // Check optional fields
  optionalFields.forEach(field => {
    if (brand[field]) completed++;
  });

  // Check array fields
  arrayFields.forEach(field => {
    if (brand[field] && brand[field].length > 0) completed++;
  });

  return Math.round((completed / total) * 100);
}

function getMissingFields(brand) {
  const missing = [];

  if (!brand.name) missing.push('name');
  if (!brand.industry) missing.push('industry');
  if (!brand.size) missing.push('size');
  if (!brand.type) missing.push('type');
  if (!brand.about) missing.push('about');
  if (!brand.story) missing.push('story');
  if (!brand.products || brand.products.length === 0) missing.push('products');

  return missing;
}

export default router;