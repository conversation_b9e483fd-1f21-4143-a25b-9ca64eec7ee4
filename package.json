{"name": "lumyn-marketing-ai", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:full": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,css,md}\"", "type-check": "tsc --noEmit"}, "dependencies": {"@fluentui/react-icons": "^2.0.305", "@hookform/resolvers": "^2.9.0", "@iconify-json/fluent": "^1.2.26", "@iconify-json/heroicons": "^1.2.2", "@iconify-json/mdi": "^1.2.3", "@iconify-json/streamline": "^1.2.5", "@iconify/react": "^6.0.0", "@tailwindcss/forms": "^0.5.10", "@types/react-datepicker": "^6.2.0", "@types/uuid": "^10.0.0", "axios": "^1.3.0", "clsx": "^1.2.0", "framer-motion": "^10.0.0", "lucide-react": "^0.216.0", "marked": "^16.1.0", "react": "^18.2.0", "react-datepicker": "^8.4.0", "react-dom": "^18.2.0", "react-hook-form": "^7.43.0", "react-icons": "^5.5.0", "react-router-dom": "^6.8.0", "recharts": "^3.1.0", "tailwind-merge": "^1.10.0", "uuid": "^11.1.0", "zod": "^3.20.0", "zustand": "^4.3.0"}, "devDependencies": {"@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@typescript-eslint/eslint-plugin": "^5.54.0", "@typescript-eslint/parser": "^5.54.0", "@vitejs/plugin-react": "^3.1.0", "autoprefixer": "^10.4.0", "eslint": "^8.35.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "postcss": "^8.4.0", "prettier": "^2.8.0", "prettier-plugin-tailwindcss": "^0.2.0", "tailwindcss": "^3.2.0", "typescript": "^5.0.0", "vite": "^4.1.0"}}