import mongoose from 'mongoose';

// Activity Schema for tracking user actions
const activitySchema = new mongoose.Schema({
  // User who performed the activity
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User ID is required']
  },
  
  // Brand associated with the activity (optional for some activities)
  brandId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Brand',
    default: null
  },
  
  // Activity categorization
  type: {
    type: String,
    required: [true, 'Activity type is required'],
    enum: [
      'brand_created',
      'brand_updated', 
      'campaign_created',
      'campaign_content_generated',
      'quickpost_generated',
      'quickpost_updated',
      'content_regenerated',
      'documents_uploaded',
      'campaign_documents_uploaded',
      'quickpost_documents_uploaded',
      'brand_analyzed'
    ]
  },
  
  // Specific action performed
  action: {
    type: String,
    required: [true, 'Action is required'],
    trim: true
  },
  
  // Human-readable title for the activity
  title: {
    type: String,
    required: [true, 'Title is required'],
    trim: true
  },
  
  // Detailed description of the activity
  description: {
    type: String,
    required: [true, 'Description is required'],
    trim: true
  },
  
  // Type of resource affected by the activity
  resourceType: {
    type: String,
    enum: ['brand', 'campaign', 'quickpost', 'document', 'content'],
    default: null
  },
  
  // ID of the affected resource
  resourceId: {
    type: mongoose.Schema.Types.ObjectId,
    default: null
  },
  
  // Additional metadata for the activity
  metadata: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better query performance
activitySchema.index({ userId: 1, createdAt: -1 });
activitySchema.index({ brandId: 1, createdAt: -1 });
activitySchema.index({ type: 1, createdAt: -1 });
activitySchema.index({ userId: 1, brandId: 1, createdAt: -1 });

// Virtual for formatted time (relative time)
activitySchema.virtual('timeAgo').get(function() {
  const now = new Date();
  const diffMs = now - this.createdAt;
  const diffMins = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  
  if (diffMins < 1) return 'Just now';
  if (diffMins < 60) return `${diffMins} minute${diffMins > 1 ? 's' : ''} ago`;
  if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
  if (diffDays < 7) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
  
  return this.createdAt.toLocaleDateString('en-US', { 
    month: 'short', 
    day: 'numeric',
    year: this.createdAt.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
  });
});

// Static method to find activities by user ID with optional filters
activitySchema.statics.findByUserId = function(userId, options = {}) {
  const { brandId, type, limit = 20, skip = 0 } = options;
  
  const query = { userId };
  
  if (brandId) {
    query.brandId = brandId;
  }
  
  if (type) {
    query.type = type;
  }
  
  return this.find(query)
    .populate('userId', 'firstName lastName email')
    .populate('brandId', 'name')
    .sort({ createdAt: -1 })
    .limit(limit)
    .skip(skip);
};

// Static method to create activity with error handling
activitySchema.statics.createActivity = async function(activityData) {
  try {
    const activity = new this(activityData);
    await activity.save();
    return activity;
  } catch (error) {
    console.error('❌ Error creating activity:', error);
    // Don't throw error to prevent breaking main operations
    return null;
  }
};

const Activity = mongoose.model('Activity', activitySchema);

export default Activity;
