import Activity from '../models/Activity.js';

/**
 * Activity Logger Service
 * Provides utility functions for logging user activities throughout the application
 */

/**
 * Log a user activity
 * @param {Object} params - Activity parameters
 * @param {string} params.userId - ID of the user performing the activity
 * @param {string} params.brandId - ID of the associated brand (optional)
 * @param {string} params.type - Type of activity (from Activity model enum)
 * @param {string} params.action - Specific action performed
 * @param {string} params.title - Human-readable title
 * @param {string} params.description - Detailed description
 * @param {string} params.resourceType - Type of resource affected (optional)
 * @param {string} params.resourceId - ID of the affected resource (optional)
 * @param {Object} params.metadata - Additional metadata (optional)
 * @returns {Promise<Activity|null>} Created activity or null if failed
 */
export const logActivity = async ({
  userId,
  brandId = null,
  type,
  action,
  title,
  description,
  resourceType = null,
  resourceId = null,
  metadata = {}
}) => {
  try {
    // Validate required parameters
    if (!userId || !type || !action || !title || !description) {
      console.warn('⚠️ Activity logging: Missing required parameters');
      return null;
    }

    const activityData = {
      userId,
      brandId,
      type,
      action,
      title,
      description,
      resourceType,
      resourceId,
      metadata
    };

    const activity = await Activity.createActivity(activityData);
    
    if (activity) {
      console.log(`✅ Activity logged: ${type} - ${title}`);
    }
    
    return activity;
  } catch (error) {
    console.error('❌ Error in activity logging service:', error);
    return null;
  }
};

/**
 * Log brand creation activity
 */
export const logBrandCreated = async (userId, brandId, brandName) => {
  return await logActivity({
    userId,
    brandId,
    type: 'brand_created',
    action: 'create_brand',
    title: 'Brand created',
    description: `Created new brand "${brandName}"`,
    resourceType: 'brand',
    resourceId: brandId,
    metadata: { brandName }
  });
};

/**
 * Log brand update activity
 */
export const logBrandUpdated = async (userId, brandId, brandName, updatedFields = []) => {
  return await logActivity({
    userId,
    brandId,
    type: 'brand_updated',
    action: 'update_brand',
    title: 'Brand updated',
    description: `Updated brand "${brandName}"${updatedFields.length ? ` (${updatedFields.join(', ')})` : ''}`,
    resourceType: 'brand',
    resourceId: brandId,
    metadata: { brandName, updatedFields }
  });
};

/**
 * Log campaign creation activity
 */
export const logCampaignCreated = async (userId, brandId, campaignId, campaignName) => {
  return await logActivity({
    userId,
    brandId,
    type: 'campaign_created',
    action: 'create_campaign',
    title: 'Campaign created',
    description: `Created new campaign "${campaignName}"`,
    resourceType: 'campaign',
    resourceId: campaignId,
    metadata: { campaignName }
  });
};

/**
 * Log campaign content generation activity
 */
export const logCampaignContentGenerated = async (userId, brandId, campaignId, campaignName, contentCount = 0) => {
  return await logActivity({
    userId,
    brandId,
    type: 'campaign_content_generated',
    action: 'generate_campaign_content',
    title: 'Campaign content generated',
    description: `Generated ${contentCount} pieces of content for "${campaignName}"`,
    resourceType: 'campaign',
    resourceId: campaignId,
    metadata: { campaignName, contentCount }
  });
};

/**
 * Log quick post generation activity
 */
export const logQuickPostGenerated = async (userId, brandId, quickPostId, channel, contentType) => {
  return await logActivity({
    userId,
    brandId,
    type: 'quickpost_generated',
    action: 'generate_quickpost',
    title: 'Quick post generated',
    description: `Generated ${contentType} content for ${channel}`,
    resourceType: 'quickpost',
    resourceId: quickPostId,
    metadata: { channel, contentType }
  });
};

/**
 * Log quick post update activity
 */
export const logQuickPostUpdated = async (userId, brandId, quickPostId, channel, updatedFields = []) => {
  return await logActivity({
    userId,
    brandId,
    type: 'quickpost_updated',
    action: 'update_quickpost',
    title: 'Quick post updated',
    description: `Updated ${channel} quick post${updatedFields.length ? ` (${updatedFields.join(', ')})` : ''}`,
    resourceType: 'quickpost',
    resourceId: quickPostId,
    metadata: { channel, updatedFields }
  });
};

/**
 * Log content regeneration activity
 */
export const logContentRegenerated = async (userId, brandId, contentId, contentType, instruction) => {
  return await logActivity({
    userId,
    brandId,
    type: 'content_regenerated',
    action: 'regenerate_content',
    title: 'Content regenerated',
    description: `Regenerated ${contentType} content with custom instructions`,
    resourceType: 'content',
    resourceId: contentId,
    metadata: { contentType, instruction: instruction?.substring(0, 100) }
  });
};

/**
 * Log document upload activity
 */
export const logDocumentsUploaded = async (userId, brandId, fileCount, uploadType = 'brand') => {
  const typeMap = {
    'brand': 'documents_uploaded',
    'campaign': 'campaign_documents_uploaded',
    'quickpost': 'quickpost_documents_uploaded'
  };

  const titleMap = {
    'brand': 'Documents uploaded',
    'campaign': 'Campaign documents uploaded',
    'quickpost': 'Quick post documents uploaded'
  };

  const descriptionMap = {
    'brand': `Uploaded ${fileCount} document${fileCount > 1 ? 's' : ''} to brand knowledge base`,
    'campaign': `Uploaded ${fileCount} document${fileCount > 1 ? 's' : ''} for campaign`,
    'quickpost': `Uploaded ${fileCount} document${fileCount > 1 ? 's' : ''} for quick post`
  };

  return await logActivity({
    userId,
    brandId,
    type: typeMap[uploadType] || 'documents_uploaded',
    action: 'upload_documents',
    title: titleMap[uploadType] || 'Documents uploaded',
    description: descriptionMap[uploadType] || `Uploaded ${fileCount} document${fileCount > 1 ? 's' : ''}`,
    resourceType: 'document',
    metadata: { fileCount, uploadType }
  });
};

/**
 * Log brand analysis activity
 */
export const logBrandAnalyzed = async (userId, brandId, brandName) => {
  return await logActivity({
    userId,
    brandId,
    type: 'brand_analyzed',
    action: 'analyze_brand',
    title: 'Brand analyzed',
    description: `AI analysis completed for "${brandName}"`,
    resourceType: 'brand',
    resourceId: brandId,
    metadata: { brandName }
  });
};

export default {
  logActivity,
  logBrandCreated,
  logBrandUpdated,
  logCampaignCreated,
  logCampaignContentGenerated,
  logQuickPostGenerated,
  logQuickPostUpdated,
  logContentRegenerated,
  logDocumentsUploaded,
  logBrandAnalyzed
};
