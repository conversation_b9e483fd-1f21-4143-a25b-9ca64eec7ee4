import express from 'express';
import { authenticate, requireUser } from '../middleware/auth.js';
import QuickPost from '../models/QuickPost.js';
import Brand from '../models/Brand.js';
import { logQuickPostUpdated } from '../utils/activityLogger.js';

const router = express.Router();

/**
 * @route   GET /api/quick-posts
 * @desc    Get all quick posts for the authenticated user
 * @access  Private
 */
router.get('/', authenticate, requireUser, async (req, res, next) => {
  try {
    const { brandId, startDate, endDate, channel, status, page = 1, limit = 10 } = req.query;
    
    // Build query
    const query = { userId: req.user._id };
    
    if (brandId) {
      query.brandId = brandId;
    }
    
    if (startDate && endDate) {
      query.postDate = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    }
    
    if (channel) {
      query.channel = channel;
    }
    
    if (status) {
      query.status = status;
    }

    // Execute query with pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const quickPosts = await QuickPost.find(query)
      .populate('brandId', 'name')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await QuickPost.countDocuments(query);

    res.status(200).json({
      success: true,
      data: quickPosts,
      pagination: {
        current: parseInt(page),
        pages: Math.ceil(total / parseInt(limit)),
        total
      }
    });
  } catch (error) {
    console.error('❌ Error fetching quick posts:', error);
    next(error);
  }
});

/**
 * @route   GET /api/quick-posts/:id
 * @desc    Get a specific quick post by ID
 * @access  Private
 */
router.get('/:id', authenticate, requireUser, async (req, res, next) => {
  try {
    const quickPost = await QuickPost.findOne({
      _id: req.params.id,
      userId: req.user._id
    }).populate('brandId', 'name');

    if (!quickPost) {
      return res.status(404).json({
        success: false,
        message: 'Quick post not found'
      });
    }

    res.status(200).json({
      success: true,
      data: quickPost
    });
  } catch (error) {
    console.error('❌ Error fetching quick post:', error);
    next(error);
  }
});

/**
 * @route   PUT /api/quick-posts/:id
 * @desc    Update a quick post
 * @access  Private
 */
router.put('/:id', authenticate, requireUser, async (req, res, next) => {
  try {
    const { status, generatedContent } = req.body;
    
    const quickPost = await QuickPost.findOne({
      _id: req.params.id,
      userId: req.user._id
    });

    if (!quickPost) {
      return res.status(404).json({
        success: false,
        message: 'Quick post not found'
      });
    }

    // Track updated fields for activity logging
    const updatedFields = [];
    if (status) {
      quickPost.status = status;
      updatedFields.push('status');
    }
    if (generatedContent) {
      quickPost.generatedContent = generatedContent;
      updatedFields.push('content');
    }

    await quickPost.save();

    // Log quick post update activity
    await logQuickPostUpdated(req.user._id, quickPost.brandId, quickPost._id, quickPost.channel, updatedFields);

    res.status(200).json({
      success: true,
      message: 'Quick post updated successfully',
      data: quickPost
    });
  } catch (error) {
    console.error('❌ Error updating quick post:', error);
    next(error);
  }
});

/**
 * @route   DELETE /api/quick-posts/:id
 * @desc    Delete a quick post
 * @access  Private
 */
router.delete('/:id', authenticate, requireUser, async (req, res, next) => {
  try {
    const quickPost = await QuickPost.findOneAndDelete({
      _id: req.params.id,
      userId: req.user._id
    });

    if (!quickPost) {
      return res.status(404).json({
        success: false,
        message: 'Quick post not found'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Quick post deleted successfully'
    });
  } catch (error) {
    console.error('❌ Error deleting quick post:', error);
    next(error);
  }
});

/**
 * @route   GET /api/quick-posts/calendar/content
 * @desc    Get quick posts for calendar view (by date range)
 * @access  Private
 */
router.get('/calendar/content', authenticate, requireUser, async (req, res, next) => {
  try {
    const { startDate, endDate, brandId } = req.query;
    
    if (!startDate || !endDate) {
      return res.status(400).json({
        success: false,
        message: 'Start date and end date are required'
      });
    }

    const query = {
      userId: req.user._id,
      postDate: {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      }
    };

    if (brandId) {
      query.brandId = brandId;
    }

    const quickPosts = await QuickPost.find(query)
      .populate('brandId', 'name')
      .sort({ postDate: 1 });

    // Transform for calendar display
    const calendarContent = quickPosts.map(post => ({
      id: post._id,
      type: 'quick-post',
      title: `Quick Post - ${post.channel}`,
      date: post.postDate,
      channel: post.channel,
      status: post.status,
      brand: post.brandId?.name || 'Unknown Brand',
      contentCount: post.contentCount,
      instructions: post.contentInstructions,
      generatedContent: post.generatedContent
    }));

    res.status(200).json({
      success: true,
      data: calendarContent
    });
  } catch (error) {
    console.error('❌ Error fetching calendar quick posts:', error);
    next(error);
  }
});

/**
 * @route   PUT /api/quick-posts/:id/content/:contentId/status
 * @desc    Update status of specific generated content
 * @access  Private
 */
router.put('/:id/content/:contentId/status', authenticate, requireUser, async (req, res, next) => {
  try {
    const { status } = req.body;
    
    if (!['generated', 'reviewed', 'approved', 'published'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid status'
      });
    }

    const quickPost = await QuickPost.findOne({
      _id: req.params.id,
      userId: req.user._id
    });

    if (!quickPost) {
      return res.status(404).json({
        success: false,
        message: 'Quick post not found'
      });
    }

    await quickPost.updateContentStatus(req.params.contentId, status);

    res.status(200).json({
      success: true,
      message: 'Content status updated successfully',
      data: quickPost
    });
  } catch (error) {
    console.error('❌ Error updating content status:', error);
    next(error);
  }
});

/**
 * @route   POST /api/quick-posts/:id/content/:contentIndex/switch-version
 * @desc    Switch content to a specific version
 * @access  Private
 */
router.post('/:id/content/:contentIndex/switch-version', authenticate, requireUser, async (req, res, next) => {
  try {
    const { id, contentIndex } = req.params;
    const { targetVersion } = req.body;

    if (!targetVersion || isNaN(parseInt(targetVersion))) {
      return res.status(400).json({
        success: false,
        message: 'Valid target version is required'
      });
    }

    // Find quick post and verify ownership
    const quickPost = await QuickPost.findOne({
      _id: id,
      userId: req.user._id
    });

    if (!quickPost) {
      return res.status(404).json({
        success: false,
        message: 'Quick post not found'
      });
    }

    const index = parseInt(contentIndex);
    if (isNaN(index) || !quickPost.generatedContent[index]) {
      return res.status(400).json({
        success: false,
        message: 'Invalid content index'
      });
    }

    // Switch to target version
    await quickPost.switchContentVersion(index, parseInt(targetVersion));

    res.status(200).json({
      success: true,
      message: 'Content version switched successfully',
      data: {
        quickPost,
        currentVersion: quickPost.generatedContent[index].version,
        availableVersions: quickPost.generatedContent[index].versionHistory?.map(v => ({
          version: v.version,
          generatedAt: v.generatedAt,
          regenerationInstruction: v.regenerationInstruction,
          generatedBy: v.generatedBy
        })) || []
      }
    });
  } catch (error) {
    console.error('❌ Error switching quick post content version:', error);
    next(error);
  }
});

/**
 * @route   GET /api/quick-posts/:id/content/:contentIndex/versions
 * @desc    Get version history for specific content
 * @access  Private
 */
router.get('/:id/content/:contentIndex/versions', authenticate, requireUser, async (req, res, next) => {
  try {
    const { id, contentIndex } = req.params;

    // Find quick post and verify ownership
    const quickPost = await QuickPost.findOne({
      _id: id,
      userId: req.user._id
    });

    if (!quickPost) {
      return res.status(404).json({
        success: false,
        message: 'Quick post not found'
      });
    }

    const index = parseInt(contentIndex);
    if (isNaN(index) || !quickPost.generatedContent[index]) {
      return res.status(400).json({
        success: false,
        message: 'Invalid content index'
      });
    }

    const content = quickPost.generatedContent[index];
    const versions = [
      // Current version
      {
        version: content.version || 1,
        generatedAt: content.generatedAt,
        regenerationInstruction: content.regenerationInstruction || '',
        generatedBy: content.generatedBy || 'ai_generation',
        isCurrent: true
      },
      // Version history
      ...(content.versionHistory || []).map(v => ({
        version: v.version,
        generatedAt: v.generatedAt,
        regenerationInstruction: v.regenerationInstruction,
        generatedBy: v.generatedBy,
        isCurrent: false
      }))
    ].sort((a, b) => b.version - a.version); // Sort by version descending

    res.status(200).json({
      success: true,
      data: {
        contentId: `${id}-${contentIndex}`,
        currentVersion: content.version || 1,
        totalVersions: versions.length,
        versions
      }
    });
  } catch (error) {
    console.error('❌ Error fetching quick post content versions:', error);
    next(error);
  }
});

export default router;
