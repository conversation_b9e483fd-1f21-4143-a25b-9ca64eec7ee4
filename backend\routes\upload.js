import express from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import FormData from 'form-data';
import axios from 'axios';
import { authenticate, requireUser } from '../middleware/auth.js';
import { ValidationError } from '../middleware/errorHandler.js';
import { Brand } from '../models/index.js';
import { logDocumentsUploaded } from '../utils/activityLogger.js';

const router = express.Router();

// Simple multer setup - store files in memory temporarily
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB per file
    files: 20 // Maximum 20 files
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain'
    ];

    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error(`File type ${file.mimetype} not allowed. Only PDF, TXT, DOC, and DOCX files are supported.`), false);
    }
  }
});

/**
 * @route   POST /api/upload-campaign-documents
 * @desc    Upload campaign-specific documents
 * @access  Private
 */
router.post('/upload-campaign-documents', authenticate, requireUser, upload.array('files', 20), async (req, res, next) => {
  try {
    const { brandId, campaignId } = req.body;
    const files = req.files;

    console.log('📁 Campaign document upload request received:', {
      brandId,
      campaignId,
      fileCount: files?.length || 0,
      files: files?.map(f => ({ name: f.originalname, size: f.size, type: f.mimetype }))
    });

    // Validate inputs
    if (!brandId) {
      return res.status(400).json({
        success: false,
        message: 'Brand ID is required'
      });
    }

    if (!campaignId) {
      return res.status(400).json({
        success: false,
        message: 'Campaign ID is required'
      });
    }

    if (!files || files.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'At least one file is required'
      });
    }

    // Verify brand exists and user has access
    const brand = await Brand.findById(brandId);
    if (!brand) {
      return res.status(404).json({
        success: false,
        message: 'Brand not found'
      });
    }

    if (brand.userId.toString() !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You can only upload documents to your own brands'
      });
    }

    // Save files locally for campaign
    const uploadDir = path.join(process.cwd(), 'uploaded_files', 'campaigns', campaignId);
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    const uploadedFiles = [];
    for (const file of files) {
      const filePath = path.join(uploadDir, file.originalname);
      fs.writeFileSync(filePath, file.buffer);
      console.log(`💾 Saved campaign file locally: ${filePath}`);

      uploadedFiles.push({
        originalName: file.originalname,
        filename: file.originalname,
        path: `uploaded_files/campaigns/${campaignId}/${file.originalname}`,
        size: file.size,
        mimetype: file.mimetype,
        uploadDate: new Date()
      });
    }

    // Call Python AI API for vector processing
    const aiServiceUrl = process.env.AI_SERVICE_URL || 'http://127.0.0.1:8000';
    console.log(`🤖 Calling Python AI service for campaign documents at ${aiServiceUrl}/upload-documents`);

    try {
      // Create FormData for Python API
      const formData = new FormData();
      formData.append('brandId', brandId);
      formData.append('campaignId', campaignId); // Add campaign ID for namespace

      // Add files from memory buffer
      files.forEach(file => {
        formData.append('files', file.buffer, {
          filename: file.originalname,
          contentType: file.mimetype
        });
      });

      // Call Python API
      const aiResponse = await axios.post(`${aiServiceUrl}/api/upload-documents`, formData, {
        headers: formData.getHeaders(),
        timeout: 120000 // 2 minutes
      });

      console.log('✅ Campaign documents processed by AI successfully');

      // Log campaign document upload activity
      await logDocumentsUploaded(req.user._id, brandId, files.length, 'campaign');

      res.status(200).json({
        success: true,
        message: `Successfully uploaded and processed ${files.length} campaign document(s)`,
        data: {
          files_uploaded: uploadedFiles.map(f => f.filename),
          upload_path: uploadDir,
          campaign_id: campaignId,
          brand_id: brandId,
          ai_processed: true,
          namespace: aiResponse.data.data?.namespace || `${brandId}_campaign_${campaignId}`
        }
      });

    } catch (aiError) {
      console.error('❌ Python AI service error for campaign documents:', aiError.message);
      console.error('❌ Error details:', {
        status: aiError.response?.status,
        statusText: aiError.response?.statusText,
        data: aiError.response?.data,
        code: aiError.code
      });

      // Still return success but indicate AI processing failed
      res.status(200).json({
        success: true,
        message: `Successfully uploaded ${files.length} campaign document(s) but AI processing failed`,
        data: {
          files_uploaded: uploadedFiles.map(f => f.filename),
          upload_path: uploadDir,
          campaign_id: campaignId,
          brand_id: brandId,
          ai_processed: false,
          ai_error: aiError.message
        }
      });
    }

  } catch (error) {
    console.error('❌ Campaign document upload failed:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to upload campaign documents. Please try again.',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @route   POST /api/upload-documents
 * @desc    Upload documents and process them with Python AI API
 * @access  Private
 */
router.post('/upload-documents', authenticate, requireUser, upload.array('files', 20), async (req, res, next) => {
  try {
    const { brandId } = req.body;
    const files = req.files;

    console.log('📁 Upload request received:', {
      brandId,
      fileCount: files?.length || 0,
      files: files?.map(f => ({ name: f.originalname, size: f.size, type: f.mimetype }))
    });

    // Validate inputs
    if (!brandId) {
      return res.status(400).json({
        success: false,
        message: 'Brand ID is required'
      });
    }

    if (!files || files.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'At least one file is required'
      });
    }

    // Verify brand exists and user has access
    const brand = await Brand.findById(brandId);
    if (!brand) {
      return res.status(404).json({
        success: false,
        message: 'Brand not found'
      });
    }

    if (brand.userId.toString() !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You can only upload documents to your own brands'
      });
    }

    // Save files locally first for future viewing
    const uploadDir = path.join(process.cwd(), 'uploaded_files', brandId);
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    for (const file of files) {
      const filePath = path.join(uploadDir, file.originalname);
      fs.writeFileSync(filePath, file.buffer);
      console.log(`💾 Saved file locally: ${filePath}`);
    }

    // Call Python AI API
    const aiServiceUrl = process.env.AI_SERVICE_URL || 'http://127.0.0.1:8000';
    console.log(`🤖 Calling Python AI service at ${aiServiceUrl}/upload-documents`);

    try {
      // Create FormData for Python API
      const formData = new FormData();
      formData.append('brandId', brandId);

      // Add files from memory buffer
      files.forEach(file => {
        formData.append('files', file.buffer, {
          filename: file.originalname,
          contentType: file.mimetype
        });
      });

      // Call Python API
      const aiResponse = await axios.post(`${aiServiceUrl}/api/upload-documents`, formData, {
        headers: formData.getHeaders(),
        timeout: 120000 // 2 minutes
      });

      console.log('✅ Python AI service completed successfully');
      console.log('📊 Python API Response:', JSON.stringify(aiResponse.data, null, 2));
      console.log('🔍 Response structure check:', {
        hasSuccess: 'success' in aiResponse.data,
        successValue: aiResponse.data.success,
        hasData: 'data' in aiResponse.data,
        dataValue: aiResponse.data.data,
        responseKeys: Object.keys(aiResponse.data)
      });

      // Save file info to database using Python API response
      if (aiResponse.data.success && aiResponse.data.data) {
        const responseData = aiResponse.data.data;
        console.log('📁 Processing files for database storage:', responseData);

        const uploadedFiles = responseData.files_uploaded.map((fileName, index) => ({
          originalName: fileName,
          filename: fileName,
          path: `uploaded_files/${brandId}/${fileName}`,
          size: files[index]?.size || 0,
          mimetype: files[index]?.mimetype || 'application/octet-stream',
          uploadDate: new Date(),
          namespace: responseData.namespace,
          vectorProcessed: true
        }));

        console.log('💾 Saving to database:', uploadedFiles);

        // Update Brand with document information
        const updateResult = await Brand.findByIdAndUpdate(brandId, {
          $push: { documents: { $each: uploadedFiles } }
        }, { new: true });

        console.log('✅ Database updated successfully');
        console.log('📊 Updated brand documents count:', updateResult.documents.length);

        // Verify the update by querying the brand again
        const verifyBrand = await Brand.findById(brandId).select('documents');
        console.log('🔍 Verification - Current documents in DB:', verifyBrand.documents.length);
        console.log('📄 Latest documents:', verifyBrand.documents.slice(-3).map(d => d.originalName));
        console.log('🆔 Brand ID being updated:', brandId);
        console.log('📋 Full documents array:', JSON.stringify(verifyBrand.documents, null, 2));
      } else {
        console.log('⚠️ No data to save - Python API response structure unexpected');
      }

      // Log document upload activity
      await logDocumentsUploaded(req.user._id, brandId, files.length, 'brand');

      // Return Python API response
      res.status(200).json(aiResponse.data);

    } catch (aiError) {
      console.error('❌ Python AI service error:', aiError.message);
      console.error('❌ Error details:', {
        status: aiError.response?.status,
        statusText: aiError.response?.statusText,
        data: aiError.response?.data,
        code: aiError.code
      });

      if (aiError.response?.data) {
        return res.status(aiError.response.status || 500).json(aiError.response.data);
      } else if (aiError.code === 'ECONNREFUSED') {
        return res.status(503).json({
          success: false,
          message: 'AI processing service is currently unavailable. Please try again later.'
        });
      } else {
        return res.status(500).json({
          success: false,
          message: `AI processing failed: ${aiError.message}`
        });
      }
    }

  } catch (error) {
    console.error('❌ Upload error:', error);

    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        message: 'File too large. Maximum size is 10MB per file.'
      });
    }

    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        success: false,
        message: 'Too many files. Maximum is 20 files per upload.'
      });
    }

    res.status(500).json({
      success: false,
      message: 'Upload failed: ' + error.message
    });
  }
});

/**
 * @route   GET /api/documents/:brandId
 * @desc    Get uploaded documents for a brand
 * @access  Private
 */
router.get('/documents/:brandId', authenticate, requireUser, async (req, res, next) => {
  try {
    const { brandId } = req.params;

    // Fetch brand with documents
    const brand = await Brand.findById(brandId).select('documents');

    if (!brand) {
      return res.status(404).json({
        success: false,
        message: 'Brand not found'
      });
    }

    const documents = brand.documents.map(doc => ({
      id: doc._id,
      name: doc.originalName,
      filename: doc.filename,
      path: doc.path,
      size: doc.size,
      mimetype: doc.mimetype,
      uploadDate: doc.uploadDate,
      type: path.extname(doc.originalName).substring(1).toUpperCase(),
      vectorProcessed: doc.vectorProcessed,
      namespace: doc.namespace
    }));

    res.json({
      success: true,
      data: { documents }
    });

  } catch (error) {
    next(error);
  }
});

/**
 * @route   GET /api/documents/:brandId/:documentId/download
 * @desc    Download a specific document
 * @access  Private
 */
router.get('/documents/:brandId/:documentId/download', authenticate, requireUser, async (req, res, next) => {
  try {
    const { brandId, documentId } = req.params;

    // Fetch brand with documents
    const brand = await Brand.findById(brandId);

    if (!brand) {
      return res.status(404).json({
        success: false,
        message: 'Brand not found'
      });
    }

    // Check if user has access to this brand
    if (brand.userId.toString() !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You can only download documents from your own brands'
      });
    }

    // Find the document by filename (since documents in array don't have _id)
    const document = brand.documents.find(doc => doc.filename === documentId || doc.originalName === documentId);

    if (!document) {
      return res.status(404).json({
        success: false,
        message: 'Document not found'
      });
    }

    // Construct file path
    const filePath = path.join(process.cwd(), document.path);

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        message: 'File not found on server'
      });
    }

    // Set appropriate headers for download
    res.setHeader('Content-Disposition', `attachment; filename="${document.originalName}"`);
    res.setHeader('Content-Type', document.mimetype);

    // Stream the file
    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);

  } catch (error) {
    console.error('❌ Download error:', error);
    next(error);
  }
});

/**
 * @route   DELETE /api/documents/:brandId/:documentId
 * @desc    Delete a specific document
 * @access  Private
 */
router.delete('/documents/:brandId/:documentId', authenticate, requireUser, async (req, res, next) => {
  try {
    const { brandId, documentId } = req.params;

    // Fetch brand with documents
    const brand = await Brand.findById(brandId);

    if (!brand) {
      return res.status(404).json({
        success: false,
        message: 'Brand not found'
      });
    }

    // Check if user has access to this brand
    if (brand.userId.toString() !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You can only delete documents from your own brands'
      });
    }

    // Find the document by filename (since documents in array don't have _id)
    const document = brand.documents.find(doc => doc.filename === documentId || doc.originalName === documentId);

    if (!document) {
      return res.status(404).json({
        success: false,
        message: 'Document not found'
      });
    }

    // Delete file from filesystem
    const filePath = path.join(process.cwd(), document.path);
    if (fs.existsSync(filePath)) {
      try {
        fs.unlinkSync(filePath);
        console.log(`🗑️ Deleted file from filesystem: ${filePath}`);
      } catch (fileError) {
        console.error(`⚠️ Failed to delete file from filesystem: ${fileError.message}`);
        // Continue with database deletion even if file deletion fails
      }
    }

    // Remove document from database by filename
    await Brand.findByIdAndUpdate(brandId, {
      $pull: {
        documents: {
          $or: [
            { filename: documentId },
            { originalName: documentId }
          ]
        }
      }
    });

    console.log(`✅ Document deleted successfully: ${document.originalName}`);

    res.status(200).json({
      success: true,
      message: 'Document deleted successfully'
    });

  } catch (error) {
    console.error('❌ Delete error:', error);
    next(error);
  }
});

/**
 * @route   POST /api/upload-quick-post-documents
 * @desc    Upload quick post specific documents
 * @access  Private
 */
router.post('/upload-quick-post-documents', authenticate, requireUser, upload.array('files', 20), async (req, res, next) => {
  try {
    const { brandId } = req.body;
    const files = req.files;

    console.log('📁 Quick post document upload request received:', {
      brandId,
      fileCount: files?.length || 0,
      files: files?.map(f => ({ name: f.originalname, size: f.size, type: f.mimetype }))
    });

    // Validate inputs
    if (!brandId) {
      return res.status(400).json({
        success: false,
        message: 'Brand ID is required'
      });
    }

    if (!files || files.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'At least one file is required'
      });
    }

    // Verify brand exists and user has access
    const brand = await Brand.findById(brandId);
    if (!brand) {
      return res.status(404).json({
        success: false,
        message: 'Brand not found'
      });
    }

    if (brand.userId.toString() !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You can only upload documents to your own brands'
      });
    }

    // Generate unique quick post session ID
    const quickPostId = `quickpost_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    // Save files locally for quick post
    const uploadDir = path.join(process.cwd(), 'uploaded_files', 'quick_posts', quickPostId);
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    const uploadedFiles = [];
    for (const file of files) {
      const filePath = path.join(uploadDir, file.originalname);
      fs.writeFileSync(filePath, file.buffer);
      console.log(`💾 Saved quick post file locally: ${filePath}`);

      uploadedFiles.push({
        originalName: file.originalname,
        filename: file.originalname,
        path: `uploaded_files/quick_posts/${quickPostId}/${file.originalname}`,
        size: file.size,
        mimetype: file.mimetype,
        uploadDate: new Date()
      });
    }

    // Call Python AI API for vector processing
    const aiServiceUrl = process.env.AI_SERVICE_URL || 'http://127.0.0.1:8000';
    console.log(`🤖 Calling Python AI service for quick post documents at ${aiServiceUrl}/upload-documents`);

    try {
      // Create FormData for Python API
      const formData = new FormData();
      formData.append('brandId', brandId);
      formData.append('quickPostId', quickPostId); // Add quick post ID for namespace

      // Add files from memory buffer
      files.forEach(file => {
        formData.append('files', file.buffer, {
          filename: file.originalname,
          contentType: file.mimetype
        });
      });

      // Call Python API
      const aiResponse = await axios.post(`${aiServiceUrl}/api/upload-documents`, formData, {
        headers: formData.getHeaders(),
        timeout: 120000 // 2 minutes
      });

      console.log('✅ Quick post documents processed by AI successfully');

      // Log quick post document upload activity
      await logDocumentsUploaded(req.user._id, brandId, files.length, 'quickpost');

      res.status(200).json({
        success: true,
        message: `Successfully uploaded and processed ${files.length} quick post document(s)`,
        data: {
          files_uploaded: uploadedFiles.map(f => f.filename),
          upload_path: uploadDir,
          quick_post_id: quickPostId,
          brand_id: brandId,
          ai_processed: true,
          namespace: aiResponse.data.data?.namespace || `${brandId}_quickpost_${quickPostId}`
        }
      });

    } catch (aiError) {
      console.error('❌ Python AI service error for quick post documents:', aiError.message);
      console.error('❌ Error details:', {
        status: aiError.response?.status,
        statusText: aiError.response?.statusText,
        data: aiError.response?.data,
        code: aiError.code
      });

      // Still return success but indicate AI processing failed
      res.status(200).json({
        success: true,
        message: `Successfully uploaded ${files.length} quick post document(s) but AI processing failed`,
        data: {
          files_uploaded: uploadedFiles.map(f => f.filename),
          upload_path: uploadDir,
          quick_post_id: quickPostId,
          brand_id: brandId,
          ai_processed: false,
          ai_error: aiError.message
        }
      });
    }

  } catch (error) {
    console.error('❌ Quick post document upload error:', error);
    next(error);
  }
});

export default router;
