import React, { useState, useEffect } from 'react'
import {
  Sparkles,
  Plus,
  Megaphone,
  FileText,
  TrendingUp,
  TrendingDown,
  Calendar,
  MessageCircle,
  CheckCircle2,
  Edit,
  Zap,
  RefreshCw,
  Upload,
  Brain,
  Activity,
} from 'lucide-react'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { Status } from '@/components/ui'
import type { CampaignStatus } from '@/components/ui'
import { useNavigate } from 'react-router-dom'
import QuickPostWizard from '@/components/modals/QuickPostWizard'
import AddBrandModal from '@/components/modals/AddBrandModal'
import { useBrandStore } from '@/stores/brandStore'
import { useAuthStore } from '@/stores/authStore'
import { useCampaignStore } from '@/stores/campaignStore'
import { activityService, getActivityConfig, type Activity } from '@/services/activityService'

interface StatCardProps {
  title: string
  value: string
  change: string
  changeType: 'positive' | 'negative'
  trendSubtitle: string
}

const StatCard: React.FC<StatCardProps> = ({ 
  title, 
  value, 
  change, 
  changeType, 
  trendSubtitle,
}) => {
  const trendConfig = {
    positive: { icon: TrendingUp, color: 'text-success-500' },
    negative: { icon: TrendingDown, color: 'text-error-500' },
  }

  const { icon: TrendIcon, color } = trendConfig[changeType];

  return (
    <div className="metric-card">
      <div className="flex flex-col justify-between h-full">
        <div>
          <div className="flex items-center justify-between">
            <p className="text-xs font-medium text-text-tertiary uppercase tracking-wider">{title}</p>
            <TrendIcon className={`w-6 h-6 ${color}`} />
          </div>
          <p className="text-3xl font-light text-text-primary mt-2 mb-1 leading-none">{value}</p>
        </div>
        <div className="flex items-center gap-2 mt-3">
          <p className={`text-sm font-medium ${color}`}>{change}</p>
          <p className="text-xs text-text-tertiary">{trendSubtitle}</p>
        </div>
      </div>
    </div>
  )
}

interface CampaignItemProps {
  id: string;
  name: string;
  status: CampaignStatus;
  progress: number;
  dueDate: string;
  contentCount: number;
}

const CampaignItem: React.FC<CampaignItemProps> = ({
  id,
  name,
  status,
  progress,
  dueDate,
  contentCount,
}) => {
  const navigate = useNavigate();

  return (
    <div 
      onClick={() => navigate(`/campaigns/${id}`)}
      className="campaign-card cursor-pointer group"
      style={{
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.1), inset 0 -1px 0 rgba(0, 0, 0, 0.2)'
      }}
    >
      <div className="flex items-start justify-between mb-3">
        <h3 className="font-medium text-text-primary text-sm">{name}</h3>
        <Status status={status} type="campaign" />
      </div>
      <div className="space-y-3">
        <div>
          <div className="flex justify-between text-xs text-text-tertiary mb-1.5">
            <span className="font-medium">Progress</span>
            <span className="font-medium text-text-secondary">{progress}%</span>
          </div>
          <div className="w-full bg-dark-quaternary rounded-full h-1.5">
            <div
              className="bg-gradient-button h-1.5 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>
        <div className="flex items-center justify-between text-xs text-text-tertiary pt-2 border-t border-gray-600">
          <div className="flex items-center gap-1.5">
            <Calendar className="w-3.5 h-3.5" />
            <span>Due {dueDate}</span>
          </div>
          <div className="flex items-center gap-1.5">
            <FileText className="w-3.5 h-3.5" />
            <span>{contentCount} pieces</span>
          </div>
        </div>
      </div>
    </div>
  )
};

const ActivityItem: React.FC<{ activity: Activity }> = ({ activity }) => {
  const config = getActivityConfig(activity.type)

  // Icon mapping
  const iconMap = {
    Plus,
    Edit,
    Zap,
    RefreshCw,
    Upload,
    Brain,
    Activity,
    MessageCircle,
    CheckCircle2,
    Megaphone
  }

  const Icon = iconMap[config.icon as keyof typeof iconMap] || Activity

  return (
    <div className="activity-card flex items-start gap-3 cursor-pointer">
      <div className={`w-8 h-8 rounded-full ${config.bgColor} flex items-center justify-center flex-shrink-0`}>
        <Icon className={`w-4 h-4 ${config.color}`} />
      </div>
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-text-primary">{activity.title}</p>
        <p className="text-xs text-text-secondary mt-1">{activity.description}</p>
        <div className="flex items-center gap-2 mt-1">
          <p className="text-xs text-text-tertiary">{activity.time}</p>
          {activity.brandName && (
            <>
              <span className="text-xs text-text-tertiary">•</span>
              <p className="text-xs text-text-tertiary">{activity.brandName}</p>
            </>
          )}
        </div>
      </div>
    </div>
  )
}

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const { brand, activeBrand, isInitialized, fetchBrand } = useBrandStore();
  const { campaigns, dashboardStats, fetchCampaigns, fetchDashboardStats } = useCampaignStore();
  const [isCreatingQuickPost, setIsCreatingQuickPost] = useState(false);
  const [showBrandModal, setShowBrandModal] = useState(false);
  const [isMandatoryBrandCreation, setIsMandatoryBrandCreation] = useState(false);

  // Activity state
  const [activities, setActivities] = useState<Activity[]>([]);
  const [activitiesLoading, setActivitiesLoading] = useState(true);
  const [activitiesError, setActivitiesError] = useState<string | null>(null);
  
  const currentHour = new Date().getHours()
  const greeting = currentHour < 12 ? 'Good morning' : currentHour < 18 ? 'Good afternoon' : 'Good evening'

  // Fetch activities function
  const fetchActivities = async (brandId?: string) => {
    try {
      setActivitiesLoading(true);
      setActivitiesError(null);
      const activities = await activityService.getRecentActivities(brandId);
      setActivities(activities);
    } catch (error: any) {
      console.error('❌ Error fetching activities:', error);
      setActivitiesError(error.message || 'Failed to load activities');
    } finally {
      setActivitiesLoading(false);
    }
  };

  // Check brand status and fetch data on component mount
  useEffect(() => {
    const initializeDashboard = async () => {
      if (user) {
        await fetchBrand();
      }
    };

    initializeDashboard();
  }, [user, fetchBrand]);

  // Handle brand modal display based on brand state
  useEffect(() => {
    // Wait for brand store to be initialized before proceeding
    if (!isInitialized) {
      console.log('📊 Dashboard: Waiting for brand store initialization...');
      return;
    }

    if (user && brand === null) {
      // No brand exists, force brand creation
      setShowBrandModal(true);
      setIsMandatoryBrandCreation(true);
    } else if (user && (brand || activeBrand)) {
      // Brand exists, fetch campaigns and dashboard stats
      // Use the improved fetchCampaigns with brandId parameter
      if (activeBrand) {
        console.log('📊 Dashboard: Fetching data for active brand:', activeBrand.name);
        fetchCampaigns(activeBrand._id);
        fetchDashboardStats(activeBrand._id);
        fetchActivities(activeBrand._id);
      } else {
        console.log('📊 Dashboard: Fetching data for all brands');
        fetchCampaigns();
        fetchDashboardStats();
        fetchActivities();
      }
    }
  }, [user, brand, activeBrand, isInitialized, fetchCampaigns, fetchDashboardStats]);

  const handleCreateQuickPost = () => {
    setIsCreatingQuickPost(true);
  };

  const handleQuickPostBack = () => {
    setIsCreatingQuickPost(false);
  };

  const handleQuickPostGenerate = (postData: any, quickPostId?: string) => {
    console.log('Generating post with data:', postData);
    console.log('Quick post ID:', quickPostId);
    setIsCreatingQuickPost(false);

    // Navigate to the generated post view page
    if (quickPostId) {
      navigate(`/quick-posts/${quickPostId}`);
    }
  };

  const handleBrandModalClose = () => {
    // Only allow closing if not mandatory or if brand was created
    if (!isMandatoryBrandCreation || brand) {
      setShowBrandModal(false);
      setIsMandatoryBrandCreation(false);
    }
  };

  // If creating quick post, show wizard
  if (isCreatingQuickPost) {
    return (
      <QuickPostWizard
        onBack={handleQuickPostBack}
        onNext={handleQuickPostGenerate}
      />
    );
  }

  return (
    <>
      <div className="space-y-6">
        {/* Welcome Section */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-text-primary">
            {greeting}, {user?.firstName || user?.fullName || 'User'}
          </h1>
          <p className="text-sm text-text-tertiary mt-1">
            Here's what's happening with your marketing today
          </p>
        </div>
        <Button
          variant="gradient"
          size="sm"
          icon={Sparkles}
          className="btn-primary"
          onClick={handleCreateQuickPost}
        >
          Quick Post
        </Button>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <StatCard
          title="Total Campaigns"
          value={dashboardStats?.totalCampaigns?.toString() || "0"}
          change={dashboardStats?.campaignsGrowth || "+0%"}
          changeType="positive"
          trendSubtitle="this month"
        />
        <StatCard
          title="Total Posts"
          value={dashboardStats?.totalPosts?.toString() || "0"}
          change={dashboardStats?.postsGrowth || "+0%"}
          changeType="positive"
          trendSubtitle="this week"
        />
        <StatCard
          title="Engagement Rate"
          value={dashboardStats?.engagementRate || "0%"}
          change={dashboardStats?.engagementGrowth || "+0%"}
          changeType="positive"
          trendSubtitle="vs last month"
        />
        <StatCard
          title="Total Reach"
          value={dashboardStats?.totalReach || "0"}
          change={dashboardStats?.reachGrowth || "+0k"}
          changeType="positive"
          trendSubtitle="this month"
        />
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 xl:grid-cols-5 gap-6">
        {/* Recent Campaigns Card */}
        <Card className="xl:col-span-3 card">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg font-semibold text-text-primary">Recent Campaigns</CardTitle>
              <Button 
                variant="secondary"
                icon={Plus} 
                size="sm"
                className="btn-secondary"
                onClick={() => navigate('/campaigns')}
              >
                Create New
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {campaigns.length > 0 ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-5">
                {campaigns.slice(0, 4).map(campaign => (
                  <CampaignItem
                    key={campaign._id}
                    id={campaign._id}
                    name={campaign.name}
                    status={campaign.status as CampaignStatus}
                    progress={campaign.progress}
                    dueDate={campaign.dueDate}
                    contentCount={campaign.contentCount}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-text-tertiary mb-4">No campaigns yet</p>
                <Button 
                  variant="gradient"
                  icon={Plus}
                  size="sm"
                  onClick={() => navigate('/campaigns')}
                >
                  Create Your First Campaign
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
        
        {/* Recent Activity Card */}
        <Card className="xl:col-span-2 card">
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-text-primary">Recent Activity</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            {activitiesLoading ? (
              <div className="space-y-3">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="flex items-start gap-3 animate-pulse">
                    <div className="w-8 h-8 bg-gray-200 rounded-full flex-shrink-0"></div>
                    <div className="flex-1 space-y-2">
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      <div className="h-3 bg-gray-200 rounded w-full"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : activitiesError ? (
              <div className="text-center py-8">
                <p className="text-text-tertiary mb-2">Failed to load activities</p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => fetchActivities(activeBrand?._id)}
                >
                  Try Again
                </Button>
              </div>
            ) : activities.length > 0 ? (
              <div className="space-y-3">
                {activities.map(activity => (
                  <ActivityItem key={activity.id} activity={activity} />
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Activity className="w-12 h-12 text-text-tertiary mx-auto mb-4" />
                <p className="text-text-tertiary mb-4">No recent activity</p>
                <p className="text-sm text-text-quaternary">
                  Start creating campaigns or quick posts to see your activity here
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>

    {/* Brand Creation Modal */}
    <AddBrandModal
      isOpen={showBrandModal}
      onClose={handleBrandModalClose}
    />
    </>
  )
}

export default Dashboard 